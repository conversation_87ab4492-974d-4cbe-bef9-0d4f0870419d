#!/usr/bin/env python3
"""
Continuous PDF to HTML Converter
Converts PDF to a continuous HTML document with proper text flow and formatting.
No page simulation - creates a single flowing document.
"""

import pdfplumber
import argparse
import os
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import re


class ContinuousPDFToHTMLConverter:
    def __init__(self, pdf_path: str, output_dir: str = "terms/html"):
        self.pdf_path = pdf_path
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # CSS for continuous document styling
        self.base_css = """
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            background-color: #fff;
        }
        
        .document-title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #000;
        }
        
        .document-subtitle {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #000;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin: 30px 0 15px 0;
            color: #000;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .paragraph {
            margin-bottom: 15px;
            text-align: justify;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .highlight {
            background-color: #ffff00;
            padding: 2px 4px;
        }
        
        .bold {
            font-weight: bold;
        }
        
        .italic {
            font-style: italic;
        }
        
        .underline {
            text-decoration: underline;
        }
        
        .center {
            text-align: center;
        }
        
        .indent {
            margin-left: 20px;
        }
        
        .quote {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #ddd;
            background-color: #f9f9f9;
            font-style: italic;
        }
        
        .link {
            color: #0066cc;
            text-decoration: underline;
        }
        
        .small-text {
            font-size: 12px;
            color: #666;
        }
        
        hr {
            border: none;
            border-top: 1px solid #ddd;
            margin: 30px 0;
        }
        
        @media print {
            body {
                max-width: none;
                margin: 0;
                padding: 20px;
            }
        }
        </style>
        """

    def extract_all_text(self) -> str:
        """Extract all text from PDF as continuous content"""
        full_text = ""
        
        with pdfplumber.open(self.pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                print(f"Extracting text from page {page_num + 1}/{len(pdf.pages)}")
                
                # Extract text with layout preservation
                page_text = page.extract_text(
                    x_tolerance=3,
                    y_tolerance=3,
                    layout=True,
                    x_density=7.25,
                    y_density=13
                )
                
                if page_text:
                    # Clean up the text
                    page_text = self._clean_text(page_text)
                    full_text += page_text + "\n\n"
        
        return full_text

    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text"""
        if not text:
            return ""
        
        # Remove excessive whitespace but preserve paragraph breaks
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # Clean each line
            line = line.strip()
            if line:
                # Fix common OCR/extraction issues
                line = re.sub(r'\s+', ' ', line)  # Multiple spaces to single space
                line = re.sub(r'([a-z])([A-Z])', r'\1 \2', line)  # Add space between lowercase and uppercase
                cleaned_lines.append(line)
            else:
                # Preserve empty lines as paragraph breaks
                if cleaned_lines and cleaned_lines[-1] != '':
                    cleaned_lines.append('')
        
        return '\n'.join(cleaned_lines)

    def parse_content_structure(self, text: str) -> List[Dict]:
        """Parse text into structured content blocks"""
        lines = text.split('\n')
        content_blocks = []
        current_paragraph = []
        
        for line in lines:
            line = line.strip()
            
            if not line:
                # Empty line - end current paragraph
                if current_paragraph:
                    paragraph_text = ' '.join(current_paragraph)
                    block_type = self._determine_block_type(paragraph_text)
                    content_blocks.append({
                        'type': block_type,
                        'content': paragraph_text,
                        'style': self._get_block_style(paragraph_text, block_type)
                    })
                    current_paragraph = []
            else:
                current_paragraph.append(line)
        
        # Don't forget the last paragraph
        if current_paragraph:
            paragraph_text = ' '.join(current_paragraph)
            block_type = self._determine_block_type(paragraph_text)
            content_blocks.append({
                'type': block_type,
                'content': paragraph_text,
                'style': self._get_block_style(paragraph_text, block_type)
            })
        
        return content_blocks

    def _determine_block_type(self, text: str) -> str:
        """Determine the type of content block"""
        text_upper = text.upper()
        text_clean = text.strip()
        
        # Title patterns
        if len(text_clean) < 50 and (
            'APP' in text_upper or 
            'TERMS AND CONDITIONS' in text_upper or
            'TERMOS E CONDIÇÕES' in text_upper
        ):
            return 'title'
        
        # Section headers (all caps, short lines)
        if (len(text_clean) < 100 and 
            text_upper == text_clean and 
            any(word in text_upper for word in ['CONTENTS', 'PROPERTY', 'CONDITIONS', 'RIGHTS', 'PRIVACY'])):
            return 'section_title'
        
        # Quotes or special formatting
        if text_clean.startswith('"') and text_clean.endswith('"'):
            return 'quote'
        
        # Default to paragraph
        return 'paragraph'

    def _get_block_style(self, text: str, block_type: str) -> Dict[str, str]:
        """Get CSS styling for a content block"""
        style = {}
        
        # Check for formatting indicators in text
        if any(indicator in text.lower() for indicator in ['link', 'http', 'www']):
            style['has_link'] = True
        
        # Check for emphasis
        if text.isupper() and len(text) < 100:
            style['emphasis'] = 'strong'
        
        return style

    def convert_to_html(self, content_blocks: List[Dict]) -> str:
        """Convert structured content to HTML"""
        html_parts = []
        
        for i, block in enumerate(content_blocks):
            content = self._escape_html(block['content'])
            block_type = block['type']
            style = block.get('style', {})
            
            # Apply formatting based on block type
            if block_type == 'title':
                if i == 0:
                    html_parts.append(f'<div class="document-title">{content}</div>')
                else:
                    html_parts.append(f'<div class="document-subtitle">{content}</div>')
            
            elif block_type == 'section_title':
                html_parts.append(f'<div class="section-title">{content}</div>')
            
            elif block_type == 'quote':
                html_parts.append(f'<div class="quote">{content}</div>')
            
            else:  # paragraph
                # Apply additional styling
                css_classes = ['paragraph']
                
                if style.get('emphasis') == 'strong':
                    css_classes.append('bold')
                
                if style.get('has_link'):
                    content = self._format_links(content)
                
                # Check for center alignment (short lines that look like titles)
                if len(content) < 100 and i < 3:
                    css_classes.append('center')
                
                class_attr = ' '.join(css_classes)
                html_parts.append(f'<div class="{class_attr}">{content}</div>')
        
        return '\n'.join(html_parts)

    def _format_links(self, text: str) -> str:
        """Format links in text"""
        # Simple link detection and formatting
        link_pattern = r'(https?://[^\s]+|www\.[^\s]+)'
        text = re.sub(link_pattern, r'<span class="link">\1</span>', text)
        return text

    def _escape_html(self, text: str) -> str:
        """Escape HTML special characters"""
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&#x27;'))

    def convert(self) -> str:
        """Convert PDF to continuous HTML document"""
        output_file = self.output_dir / f"{Path(self.pdf_path).stem}_continuous.html"
        
        print("🚀 Starting continuous PDF to HTML conversion...")
        
        # Extract all text
        full_text = self.extract_all_text()
        
        if not full_text.strip():
            raise ValueError("No text could be extracted from the PDF")
        
        print("📝 Parsing content structure...")
        
        # Parse into structured content
        content_blocks = self.parse_content_structure(full_text)
        
        print(f"📋 Found {len(content_blocks)} content blocks")
        
        # Convert to HTML
        html_body = self.convert_to_html(content_blocks)
        
        # Create complete HTML document
        html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{Path(self.pdf_path).stem}</title>
    {self.base_css}
</head>
<body>
{html_body}
</body>
</html>"""
        
        # Save the HTML file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ Continuous conversion completed!")
        print(f"📄 Input: {self.pdf_path}")
        print(f"🌐 Output: {output_file}")
        
        return str(output_file)


def main():
    parser = argparse.ArgumentParser(description='Convert PDF to continuous HTML document')
    parser.add_argument('pdf_file', help='Path to the PDF file to convert')
    parser.add_argument('-o', '--output', default='terms/html', 
                       help='Output directory (default: terms/html)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.pdf_file):
        print(f"Error: PDF file '{args.pdf_file}' not found!")
        return 1
    
    try:
        converter = ContinuousPDFToHTMLConverter(args.pdf_file, args.output)
        output_file = converter.convert()
        print(f"\n🎉 Successfully converted '{args.pdf_file}' to '{output_file}'")
        return 0
    except Exception as e:
        print(f"❌ Error during conversion: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
