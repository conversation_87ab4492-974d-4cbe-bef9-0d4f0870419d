#!/usr/bin/env python3
"""
Test script for the continuous PDF to HTML converter
"""

from pdf_to_html_continuous import ContinuousPDFToHTMLConverter
import os

def main():
    # Check if dock.pdf exists
    pdf_file = "dock.pdf"
    if not os.path.exists(pdf_file):
        print(f"Error: {pdf_file} not found in current directory!")
        return
    
    print(f"📄 Converting {pdf_file} to CONTINUOUS HTML...")
    print("This version creates a single flowing document without page simulation.")
    print("Features:")
    print("  • Continuous text flow")
    print("  • Proper paragraph formatting")
    print("  • Automatic section detection")
    print("  • Clean typography")
    print("  • Responsive design")
    print()
    
    # Create continuous converter instance
    converter = ContinuousPDFToHTMLConverter(pdf_file, "terms/html")
    
    try:
        # Convert the PDF
        output_file = converter.convert()
        print(f"\n🎯 Key features of this conversion:")
        print(f"   ✓ Single continuous document (no pages)")
        print(f"   ✓ Proper text flow and paragraphs")
        print(f"   ✓ Automatic title and section detection")
        print(f"   ✓ Clean, readable typography")
        print(f"   ✓ Responsive design for all devices")
        print(f"   ✓ Print-friendly styling")
        print(f"\n🌐 Open the HTML file in your browser:")
        print(f"   {output_file}")
        
    except Exception as e:
        print(f"❌ Error during continuous conversion: {e}")
        print("Make sure you have installed the required dependencies:")
        print("pip install -r requirements.txt")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
